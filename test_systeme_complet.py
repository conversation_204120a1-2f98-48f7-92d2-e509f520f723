#!/usr/bin/env python3
"""
Test complet du système BCT après optimisation INDEX 3
Vérification de la cohérence et fonctionnalité complète
"""

import sys
import os
sys.path.append('.')

from bct import (
    AZRConfig, BaccaratGame, BaccaratHand, BaccaratCountingEngine,
    AZRClusterManager
)
from datetime import datetime

def test_configuration():
    """Test de la configuration après optimisation"""
    print("🔧 TEST CONFIGURATION")
    print("-" * 30)
    
    config = AZRConfig()
    
    # Vérifier les INDEX
    print(f"✅ INDEX 1 - Cartes: {list(config.card_count_categories.keys())}")
    print(f"✅ INDEX 2 - SYNC/DESYNC: {config.sync_states}")
    print(f"✅ INDEX 3 - Résultats: {config.game_results}")
    print(f"✅ INDEX 4 - Conversions S/O: {config.so_conversions}")
    
    # Vérifier qu'il n'y a plus de combined_states
    has_combined_states = hasattr(config, 'combined_states')
    print(f"✅ Combined states supprimés: {not has_combined_states}")
    
    return True

def test_baccarathand_optimized():
    """Test de la structure BaccaratHand optimisée"""
    print("\n🎯 TEST BACCARATHAND OPTIMISÉ")
    print("-" * 35)
    
    # Créer une main de test
    hand = BaccaratHand(
        hand_number=1,
        pb_hand_number=1,
        cards_distributed=5,
        cards_parity='IMPAIR',
        cards_category='impair_5',
        sync_state='SYNC',
        result='PLAYER',
        so_conversion='--'
    )
    
    # Tester la propriété calculée
    expected = 'impair_5_sync'
    actual = hand.combined_state
    
    print(f"Cards category: {hand.cards_category}")
    print(f"Sync state: {hand.sync_state}")
    print(f"Combined state (calculé): {actual}")
    print(f"Expected: {expected}")
    print(f"✅ Propriété calculée: {actual == expected}")
    
    # Tester différents cas
    test_cases = [
        ('pair_4', 'DESYNC', 'pair_4_desync'),
        ('pair_6', 'SYNC', 'pair_6_sync'),
        ('impair_5', 'DESYNC', 'impair_5_desync')
    ]
    
    all_passed = True
    for cards_cat, sync_st, expected_combined in test_cases:
        test_hand = BaccaratHand(
            hand_number=1, pb_hand_number=1, cards_distributed=4,
            cards_parity='PAIR', cards_category=cards_cat,
            sync_state=sync_st, result='PLAYER', so_conversion='--'
        )
        
        actual_combined = test_hand.combined_state
        passed = actual_combined == expected_combined
        all_passed = all_passed and passed
        
        status = "✅" if passed else "❌"
        print(f"{status} {cards_cat} + {sync_st} = {actual_combined}")
    
    return all_passed

def test_counting_engine():
    """Test du moteur de comptage après optimisation"""
    print("\n⚙️ TEST MOTEUR DE COMPTAGE")
    print("-" * 30)
    
    config = AZRConfig()
    engine = BaccaratCountingEngine(config)
    
    # Créer un jeu de test
    game = BaccaratGame(
        game_number=1,
        burn_cards_count=4,
        burn_parity='PAIR',
        initial_sync_state='SYNC',
        current_sync_state='SYNC'
    )
    
    # Traiter quelques mains
    test_hands = [
        ('PLAYER', 5, 'impair_5'),
        ('BANKER', 4, 'pair_4'),
        ('PLAYER', 6, 'pair_6'),
        ('TIE', 5, 'impair_5')
    ]
    
    print("Traitement des mains:")
    all_passed = True
    
    for i, (result, total_cards, expected_category) in enumerate(test_hands, 1):
        hand = engine.process_hand(game, result, total_cards, expected_category)
        
        # Vérifications
        category_ok = hand.cards_category == expected_category
        combined_ok = hand.combined_state == f"{expected_category}_{hand.sync_state.lower()}"
        
        status = "✅" if (category_ok and combined_ok) else "❌"
        print(f"{status} Main {i}: {result} {total_cards}c -> {hand.combined_state}")
        
        all_passed = all_passed and category_ok and combined_ok
    
    return all_passed

def test_json_serialization():
    """Test de la sérialisation JSON (sauvegarde)"""
    print("\n💾 TEST SÉRIALISATION JSON")
    print("-" * 30)
    
    # Créer une main
    hand = BaccaratHand(
        hand_number=1,
        pb_hand_number=1,
        cards_distributed=5,
        cards_parity='IMPAIR',
        cards_category='impair_5',
        sync_state='SYNC',
        result='PLAYER',
        so_conversion='--'
    )
    
    # Simuler la sérialisation comme dans le code
    hand_data = {
        'hand_number': hand.hand_number,
        'pb_hand_number': hand.pb_hand_number,
        'cards_distributed': hand.cards_distributed,
        'cards_parity': hand.cards_parity,
        'cards_category': hand.cards_category,
        'sync_state': hand.sync_state,
        'combined_state': hand.combined_state,  # Propriété calculée
        'result': hand.result,
        'so_conversion': hand.so_conversion,
        'timestamp': hand.timestamp.isoformat()
    }
    
    # Vérifier que combined_state est bien inclus
    has_combined = 'combined_state' in hand_data
    correct_value = hand_data['combined_state'] == 'impair_5_sync'
    
    print(f"✅ Combined state dans JSON: {has_combined}")
    print(f"✅ Valeur correcte: {correct_value}")
    print(f"Valeur: {hand_data['combined_state']}")
    
    return has_combined and correct_value

def test_interface_compatibility():
    """Test de compatibilité avec l'interface"""
    print("\n🖥️ TEST COMPATIBILITÉ INTERFACE")
    print("-" * 35)
    
    # Simuler l'utilisation dans l'interface
    hand = BaccaratHand(
        hand_number=1,
        pb_hand_number=1,
        cards_distributed=4,
        cards_parity='PAIR',
        cards_category='pair_4',
        sync_state='DESYNC',
        result='BANKER',
        so_conversion='O'
    )
    
    # Simuler les affichages de l'interface
    try:
        # Log message (ligne 1046)
        log_msg = f"Main traitée: {hand.result} {hand.cards_distributed} cartes -> {hand.combined_state} {hand.so_conversion}"
        
        # Stats display (ligne 1110)
        stats_msg = f"  • État combiné: {hand.combined_state}\n"
        
        # Details display (ligne 1240)
        details_msg = f"  Main {hand.hand_number}: {hand.result} {hand.cards_distributed}c → {hand.combined_state} ({hand.so_conversion})\n"
        
        print(f"✅ Log: {log_msg}")
        print(f"✅ Stats: {stats_msg.strip()}")
        print(f"✅ Details: {details_msg.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur interface: {e}")
        return False

def run_complete_system_test():
    """Exécute tous les tests du système"""
    print("🧪 TEST COMPLET DU SYSTÈME BCT OPTIMISÉ")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("BaccaratHand Optimisé", test_baccarathand_optimized),
        ("Moteur de Comptage", test_counting_engine),
        ("Sérialisation JSON", test_json_serialization),
        ("Compatibilité Interface", test_interface_compatibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
        except Exception as e:
            results.append((test_name, False, str(e)))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"    Erreur: {error}")
        if success:
            passed += 1
    
    print("-" * 50)
    print(f"Résultat: {passed}/{total} tests réussis ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Le système est fonctionnel et cohérent")
        print("✅ L'optimisation INDEX 3 est un succès")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️ Vérification nécessaire")
    
    return passed == total

if __name__ == "__main__":
    success = run_complete_system_test()
    sys.exit(0 if success else 1)
