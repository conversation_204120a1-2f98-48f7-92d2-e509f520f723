#!/usr/bin/env python3
"""
Test de validation des ajustements BCT
Vérification de l'exploitation des influences TIE
"""

import sys
import os
sys.path.append('.')

from bct import (
    AZRConfig, BaccaratGame, BaccaratHand, BaccaratCountingEngine,
    AZRClusterManager
)
from datetime import datetime

def test_nouvelles_methodes_acces():
    """Test des nouvelles méthodes d'accès enrichi"""
    print("🔬 TEST NOUVELLES MÉTHODES D'ACCÈS")
    print("-" * 40)
    
    config = AZRConfig()
    engine = BaccaratCountingEngine(config)
    
    # Créer un jeu avec séquence incluant TIE
    game = BaccaratGame(
        game_number=1,
        burn_cards_count=4,
        burn_parity='PAIR',
        initial_sync_state='SYNC',
        current_sync_state='SYNC'
    )
    
    # Séquence de test : P → B → T → T → B
    test_sequence = [
        ('PLAYER', 4, 'pair_4'),
        ('BANKER', 5, 'impair_5'),
        ('TIE', 4, 'pair_4'),
        ('TIE', 6, 'pair_6'),
        ('BANKER', 4, 'pair_4')
    ]
    
    print("Traitement de la séquence:")
    for i, (result, cards, category) in enumerate(test_sequence, 1):
        hand = engine.process_hand(game, result, cards, category)
        print(f"  Main {i}: {result} {cards}c → {hand.combined_state}")
    
    print(f"\n📊 VALIDATION NOUVELLES MÉTHODES:")
    
    # Test get_full_influence_sequence
    full_influences = game.get_full_influence_sequence()
    print(f"✅ get_full_influence_sequence: {len(full_influences)} influences")
    
    # Test get_sync_pattern_complete
    sync_pattern = game.get_sync_pattern_complete()
    print(f"✅ get_sync_pattern_complete: {sync_pattern}")
    
    # Test get_combined_states_all
    combined_states = game.get_combined_states_all()
    print(f"✅ get_combined_states_all: {combined_states}")
    
    # Test get_parity_pattern_complete
    parity_pattern = game.get_parity_pattern_complete()
    print(f"✅ get_parity_pattern_complete: {parity_pattern}")
    
    # Test get_algorithmic_summary
    summary = game.get_algorithmic_summary()
    print(f"✅ get_algorithmic_summary: {len(summary)} clés disponibles")
    
    return True

def test_exploitation_tie():
    """Test de l'exploitation des influences TIE"""
    print("\n🎯 TEST EXPLOITATION INFLUENCES TIE")
    print("-" * 40)
    
    config = AZRConfig()
    engine = BaccaratCountingEngine(config)
    
    game = BaccaratGame(
        game_number=1,
        burn_cards_count=3,
        burn_parity='IMPAIR',
        initial_sync_state='DESYNC',
        current_sync_state='DESYNC'
    )
    
    # Séquence avec plusieurs TIE
    sequence = [
        ('PLAYER', 5, 'impair_5'),  # Main 1
        ('TIE', 4, 'pair_4'),       # Main 2 - TIE avec influence
        ('TIE', 6, 'pair_6'),       # Main 3 - TIE avec influence
        ('BANKER', 5, 'impair_5')   # Main 4
    ]
    
    for result, cards, category in sequence:
        engine.process_hand(game, result, cards, category)
    
    # Obtenir résumé algorithmique
    summary = game.get_algorithmic_summary()
    
    print("📈 RICHESSE DES DONNÉES:")
    print(f"  • Total influences: {len(summary['full_influences'])}")
    print(f"  • Manches P/B: {summary['pb_hands']}")
    print(f"  • TIE: {summary['tie_hands']}")
    print(f"  • Pattern SYNC complet: {summary['sync_pattern']}")
    
    print("\n🔍 INFLUENCES TIE DÉTAILLÉES:")
    for influence in summary['full_influences']:
        if influence['is_tie']:
            print(f"  • Main {influence['hand_number']}: {influence['combined_state']} (TIE exploité)")
    
    print("\n✅ VALIDATION EXPLOITATION:")
    # Vérifier que les TIE sont dans les influences complètes
    tie_count = sum(1 for inf in summary['full_influences'] if inf['is_tie'])
    print(f"  • TIE dans influences complètes: {tie_count}")
    
    # Vérifier continuité pattern SYNC
    sync_continuity = len(summary['sync_pattern']) == len(summary['full_influences'])
    print(f"  • Continuité pattern SYNC: {sync_continuity}")
    
    # Vérifier richesse états combinés
    combined_richness = len(summary['combined_states']) == len(summary['full_influences'])
    print(f"  • Richesse états combinés: {combined_richness}")
    
    return tie_count > 0 and sync_continuity and combined_richness

def test_algorithmes_enrichis():
    """Test de l'accès enrichi des algorithmes"""
    print("\n🤖 TEST ALGORITHMES ENRICHIS")
    print("-" * 40)
    
    config = AZRConfig()
    cluster_manager = AZRClusterManager(config)
    engine = BaccaratCountingEngine(config)
    
    game = BaccaratGame(
        game_number=1,
        burn_cards_count=4,
        burn_parity='PAIR',
        initial_sync_state='SYNC',
        current_sync_state='SYNC'
    )
    
    # Ajouter quelques mains
    test_hands = [
        ('PLAYER', 4, 'pair_4'),
        ('TIE', 5, 'impair_5'),
        ('BANKER', 6, 'pair_6')
    ]
    
    for result, cards, category in test_hands:
        engine.process_hand(game, result, cards, category)
    
    # Tester accès enrichi des clusters
    cluster_results = cluster_manager.process_all_clusters(game)
    
    print("📊 RÉSULTATS CLUSTERS ENRICHIS:")
    print(f"  • Clusters participants: {cluster_results['consensus']['participating_clusters']}")
    
    if 'global_exploitation' in cluster_results:
        exploitation = cluster_results['global_exploitation']
        print(f"  • Influences totales disponibles: {exploitation['total_influences_available']}")
        print(f"  • Influences TIE exploitées: {exploitation['tie_influences_exploited']}")
        print(f"  • Références P/B: {exploitation['pb_references']}")
        print(f"  • Continuité pattern SYNC: {exploitation['sync_pattern_continuity']}")
    
    # Tester rollout individuel
    cluster_0 = cluster_manager.clusters[0]
    analysis = cluster_0.analyzer.analyze_game_state(game)
    
    print(f"\n🔬 ANALYSE ROLLOUT ENRICHIE:")
    if 'data_access' in analysis:
        data_access = analysis['data_access']
        print(f"  • Total influences: {data_access['total_influences']}")
        print(f"  • TIE hands: {data_access['tie_hands']}")
        print(f"  • Pattern SYNC récent: {data_access['recent_sync_pattern']}")
    
    return True

def run_validation_complete():
    """Exécute tous les tests de validation"""
    print("🧪 VALIDATION AJUSTEMENTS BCT")
    print("=" * 50)
    
    tests = [
        ("Nouvelles Méthodes d'Accès", test_nouvelles_methodes_acces),
        ("Exploitation Influences TIE", test_exploitation_tie),
        ("Algorithmes Enrichis", test_algorithmes_enrichis)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
        except Exception as e:
            results.append((test_name, False, str(e)))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ VALIDATION")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success, error in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if error:
            print(f"    Erreur: {error}")
        if success:
            passed += 1
    
    print("-" * 50)
    print(f"Résultat: {passed}/{total} tests réussis ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 TOUS LES AJUSTEMENTS VALIDÉS!")
        print("✅ Exploitation TIE opérationnelle")
        print("✅ Algorithmes enrichis fonctionnels")
        print("✅ Accès données complet")
    else:
        print("❌ CERTAINS AJUSTEMENTS À CORRIGER")
    
    return passed == total

if __name__ == "__main__":
    success = run_validation_complete()
    sys.exit(0 if success else 1)
