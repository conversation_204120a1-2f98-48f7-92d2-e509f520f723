#!/usr/bin/env python3
"""
Test de l'optimisation INDEX 3 - Suppression de la redondance
"""

from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional

@dataclass
class BaccaratHandOptimized:
    """Version optimisée sans INDEX 3 redondant"""
    
    # Identification
    hand_number: int
    pb_hand_number: Optional[int]
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int
    cards_parity: str
    cards_category: str
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str
    
    # INDEX 3 : Résultat (ancien INDEX 4)
    result: str
    
    # INDEX 4 : Conversion S/O (ancien INDEX 5)
    so_conversion: str
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)
    
    @property
    def combined_state(self) -> str:
        """
        Calcule l'état combiné à la demande (ancien INDEX 3)
        Combine INDEX 1 (cards_category) + INDEX 2 (sync_state)
        """
        return f"{self.cards_category}_{self.sync_state.lower()}"

def test_optimisation():
    """Test de l'optimisation"""
    
    print("🧪 TEST DE L'OPTIMISATION INDEX 3")
    print("=" * 50)
    
    # Test 1 : impair_5_sync
    hand1 = BaccaratHandOptimized(
        hand_number=1,
        pb_hand_number=1,
        cards_distributed=5,
        cards_parity='IMPAIR',
        cards_category='impair_5',
        sync_state='SYNC',
        result='PLAYER',
        so_conversion='--'
    )
    
    # Test 2 : pair_4_desync
    hand2 = BaccaratHandOptimized(
        hand_number=2,
        pb_hand_number=2,
        cards_distributed=4,
        cards_parity='PAIR',
        cards_category='pair_4',
        sync_state='DESYNC',
        result='BANKER',
        so_conversion='O'
    )
    
    # Test 3 : pair_6_sync
    hand3 = BaccaratHandOptimized(
        hand_number=3,
        pb_hand_number=3,
        cards_distributed=6,
        cards_parity='PAIR',
        cards_category='pair_6',
        sync_state='SYNC',
        result='PLAYER',
        so_conversion='S'
    )
    
    # Vérifications
    tests = [
        (hand1, 'impair_5_sync'),
        (hand2, 'pair_4_desync'),
        (hand3, 'pair_6_sync')
    ]
    
    all_passed = True
    
    for i, (hand, expected) in enumerate(tests, 1):
        actual = hand.combined_state
        passed = actual == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"Test {i}: {status}")
        print(f"  Cards: {hand.cards_category}")
        print(f"  Sync: {hand.sync_state}")
        print(f"  Combined: {actual}")
        print(f"  Expected: {expected}")
        print()
    
    # Résumé
    print("=" * 50)
    if all_passed:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ L'optimisation fonctionne parfaitement")
        print("✅ INDEX 3 supprimé avec succès")
        print("✅ Propriété calculée opérationnelle")
    else:
        print("❌ ÉCHEC DES TESTS")
    
    print("=" * 50)
    
    # Avantages de l'optimisation
    print("📊 AVANTAGES DE L'OPTIMISATION:")
    print("• Réduction de 20% du nombre d'INDEX (5→4)")
    print("• Suppression de la redondance de données")
    print("• Calcul à la demande (plus efficace)")
    print("• Code plus maintenable")
    print("• Aucune perte de fonctionnalité")
    
    return all_passed

if __name__ == "__main__":
    test_optimisation()
