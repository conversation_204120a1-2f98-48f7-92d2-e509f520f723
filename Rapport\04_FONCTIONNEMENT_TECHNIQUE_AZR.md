# FONCTIONNEMENT TECHNIQUE DÉTAILLÉ D'AZR

## 📋 INFORMATIONS TECHNIQUES

**Date :** 12 juin 2025  
**Sources :** text_pages/page_007.txt + page_008.txt + equations_synthesis.txt  
**Objectif :** Comprendre le fonctionnement interne complet d'AZR  
**Niveau :** Documentation technique approfondie  

---

## 🔧 ALGORITHME COMPLET D'AZR

### Algorithm 1: Self-Play Training of Absolute Zero Reasoner (AZR)

#### **Prérequis**
- **Pretrained base LLM** : π_θ (modèle de base pré-entraîné)
- **Batch size** : B (taille de batch)
- **#references** : K (nombre de références)
- **Iterations** : T (nombre d'itérations)

#### **Phase d'initialisation**
```
1: D_ded, D_abd, D_ind ← InitSeeding(π_θ)  ▷ voir §3.3.1
```

#### **Boucle d'entraînement principal**
```
2: for t ← 1 to T do
3:   for b ← 1 to B do                     ▷ PROPOSE PHASE
4:     p ∼ D_abd ∪ D_ded                   ▷ échantillonner un programme pour proposition induction
5:     {i_n^π}_{n=1}^N, m_π ← π^{propose}_θ(ind, p)  ▷ générer N entrées et description
6:     if valid then
7:       D_ind ← D_ind ∪ {(p, {i_n^π, o_n^π}, m_π)}  ▷ mettre à jour buffer induction
8:   for α ∈ {ded, abd} do
9:     for b ← 1 to B do
10:      (p_π, i_π, o_π) ← π^{propose}_θ(α, K_refs)  ▷ proposer tâche
11:      if valid then
12:        D_α ← D_α ∪ {(p_π, i_π, o_π)}  ▷ mettre à jour buffers déduction/abduction
13:  for all α ∈ {ded, abd, ind} do        ▷ SOLVE PHASE
14:    for b ← 1 to B do
15:      y_π ∼ π^{solve}_θ(·|x)           ▷ résoudre tâche
16:  Reward: Use proposed task triplets and solved answers to get r^{propose} & r^{solve}
17:  RL update: use Task Relative REINFORCE++ to update π_θ
```

---

## 🗂️ GESTION DES BUFFERS ET TÂCHES

### 3.3.2. Task Proposal Inputs and Buffer Management

#### **Trois utilisations des buffers**

1. **Pour les proposers abduction/déduction**
   > *"We uniformly sample K past triplets from the buffer, present them as in-context examples to the proposer and let it generate a new task. The design is to show it past examples, and prompt it to generate a different one to promote diversity"* (Page 7)

2. **Pour le proposer induction**
   > *"We sample one triplet from the union of abduction and deduction buffers D_abd ∪ D_ded, and present the program p from that triplet to the induction proposer to generate a set of N matching inputs {i_n} and a natural language message m"* (Page 7)

3. **Pour la stabilité d'entraînement**
   > *"To maintain stable training, if a batch of solver problems contains fewer than B valid proposed tasks (proposer not adhering to formatting), we fill the remainder by uniformly sampling from the corresponding task buffer of previously validated triplets"* (Page 7)

#### **Croissance des buffers**
- **Abduction/Déduction** : "The buffer grows for abduction and deduction tasks whenever π propose a valid triplet (p, i, o), regardless if it gets any task reward"
- **Induction** : "Similarly, for induction tasks, all valid triplets (p, {i_n, o_n}), m are added to the buffer"

---

## ✅ VALIDATION ET VÉRIFICATION DES TÂCHES

### 3.3.3. Constructing Valid Tasks

#### **Validation des propositions de tâches**

**Pour déduction et abduction :**
> *"For deduction and abduction tasks, each proposal consists of a program and an input (p, i). To validate the task, we use the task validation procedure on the input to obtain the correct output o, resulting in a complete triplet (p, i, o)"* (Page 7)

**Pour induction :**
> *"For induction tasks, given a program p the policy proposes a set of inputs {i_n} and message m. We also use the task validation procedure on each of the input i_n"* (Page 7)

#### **Programmes déterministes - Équation (7)**

```
∀p ∈ P_deterministic, ∀i ∈ I, lim_{j→∞} p(i)^{(1)} = p(i)^{(2)} = ... = p(i)^{(j)}
```

**Explication technique :**
> *"Where (j) indexes repeated independent executions of the program. That is, for all inputs i, the output of p(i) remains identical with any independent execution of the program"* (Page 8)

**Implémentation pratique :**
> *"To implement the filtering of invalid probabilistic programs, and following the definition of a deterministic program highlighted in Equation (7), we approximate this procedure by independently running the program j finite times and checking that all the outputs are equal. For computational budget reasons, we fixed j = 2 for all experiments"* (Page 8)

**Justification :**
> *"Since the output of probabilistic programs can vary on every individual run, it is non-trivial to use verifiable functions to evaluate the correctness of an answer. Therefore, to keep the verifier simple, we restrict the valid programs generated by the learner to the class of deterministic programs"* (Page 8)

---

## 🔍 VÉRIFICATION DES RÉPONSES

### 3.3.4. Answer Verification

#### **Vérification par type de tâche**

**Abduction :**
```
p(i_π) = p(i⋆)
```
> *"For abduction task, we receive i_π from the solver policy, then we equivalence match using p(i_π) = p(i⋆), where ⋆ refers to the"* (Page 8)

**Déduction :**
```
o_π = o⋆
```
> *"For deduction task, we receive o_π from the solver policy, then we equivalence match using o_π = o⋆"* (Page 8)

**Induction :**
```
all({p_π(i⋆_n) = o⋆_n}_N)
```
> *"For induction task, we receive p_π from the solver policy, then we equivalence match using all({p_π(i⋆_n) = o⋆_n}_N). This part might be convoluted to explain in language, therefore we recommend the reader to see how we did abduction, deduction and induction verification in code in Figures 10 to 12, respectively"* (Page 8)

---

## ⚡ TASK-RELATIVE REINFORCE++ (TRR++)

### 3.3.5. Innovation algorithmique

#### **Contexte multitâche**
> *"Since AZR trains the combination of roles and task types, it operates in a multitask reinforcement learning setup"* (Page 8)

#### **Amélioration par rapport à REINFORCE++**
> *"Instead of computing a single global baseline as in REINFORCE++, we compute separate baselines for each of the six task-role configurations. This can be viewed as an interpolation between per-question baselines, as in GRPO, and a global baseline, allowing for more structured variance reduction tailored to each task setup"* (Page 8)

#### **Six configurations task-role**
1. **Déduction-Propose**
2. **Déduction-Solve**
3. **Abduction-Propose**
4. **Abduction-Solve**
5. **Induction-Propose**
6. **Induction-Solve**

#### **Avantage normalisé**
```
A^{norm}_{task,role} = (r - μ_{task,role}) / σ_{task,role}
```
> *"The normalized advantage A^{norm} is computed as"* (Page 8)

---

## 🧮 IMPLÉMENTATION TECHNIQUE COMPLÈTE

```python
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import subprocess
import tempfile
import os

class AZRTechnicalImplementation:
    """
    Implémentation technique complète d'AZR basée sur l'algorithme détaillé
    """
    
    def __init__(self, batch_size: int = 64, k_references: int = 5, 
                 iterations: int = 1000, j_deterministic: int = 2):
        self.B = batch_size
        self.K = k_references
        self.T = iterations
        self.j = j_deterministic  # Vérifications déterministes
        
        # Buffers pour les trois types de tâches
        self.D_ded = []  # Buffer déduction
        self.D_abd = []  # Buffer abduction
        self.D_ind = []  # Buffer induction
        
        # Statistiques pour TRR++
        self.task_role_stats = defaultdict(lambda: {'rewards': [], 'mean': 0.0, 'std': 1.0})
    
    def init_seeding(self, model: nn.Module) -> None:
        """
        Initialisation des buffers avec des triplets seed
        """
        # Triplet identité minimal pour bootstrap
        identity_triplet = {
            'program': 'def f(x): return x',
            'input': 'Hello World',
            'output': 'Hello World'
        }
        
        # Initialiser tous les buffers avec le triplet seed
        self.D_ded.append(identity_triplet)
        self.D_abd.append(identity_triplet)
        self.D_ind.append(identity_triplet)
    
    def self_play_training(self, model: nn.Module) -> None:
        """
        Algorithme 1: Self-Play Training of Absolute Zero Reasoner (AZR)
        """
        # 1: Initialisation
        self.init_seeding(model)
        
        # 2: Boucle d'entraînement principal
        for t in range(1, self.T + 1):
            
            # PROPOSE PHASE
            proposed_tasks = self.propose_phase(model)
            
            # SOLVE PHASE
            solved_tasks = self.solve_phase(model, proposed_tasks)
            
            # REWARD CALCULATION
            rewards = self.calculate_rewards(proposed_tasks, solved_tasks)
            
            # RL UPDATE avec TRR++
            self.trr_plus_plus_update(model, rewards)
    
    def propose_phase(self, model: nn.Module) -> Dict[str, List]:
        """
        Phase de proposition de tâches pour les trois types
        """
        proposed_tasks = {'ded': [], 'abd': [], 'ind': []}
        
        # Proposition pour induction
        for b in range(self.B):
            # 4: Échantillonner programme des buffers abd ∪ ded
            p = self.sample_program_from_union()
            
            # 5: Générer N entrées et description
            inputs, message = self.propose_induction_task(model, p)
            
            # 6-7: Validation et mise à jour buffer
            if self.validate_induction_task(p, inputs, message):
                task = {'program': p, 'inputs': inputs, 'message': message}
                proposed_tasks['ind'].append(task)
                self.D_ind.append(task)
        
        # Proposition pour déduction et abduction
        for task_type in ['ded', 'abd']:
            for b in range(self.B):
                # 10: Proposer tâche avec K références
                k_refs = self.sample_k_references(task_type)
                program, input_val, output_val = self.propose_task(model, task_type, k_refs)
                
                # 11-12: Validation et mise à jour buffer
                if self.validate_task(program, input_val, output_val):
                    task = {'program': program, 'input': input_val, 'output': output_val}
                    proposed_tasks[task_type].append(task)
                    getattr(self, f'D_{task_type}').append(task)
        
        return proposed_tasks
    
    def solve_phase(self, model: nn.Module, proposed_tasks: Dict) -> Dict:
        """
        Phase de résolution des tâches proposées
        """
        solved_tasks = {}
        
        for task_type in ['ded', 'abd', 'ind']:
            solved_tasks[task_type] = []
            
            for task in proposed_tasks[task_type]:
                # 15: Résoudre tâche
                solution = self.solve_task(model, task_type, task)
                solved_tasks[task_type].append({
                    'task': task,
                    'solution': solution
                })
        
        return solved_tasks
    
    def validate_deterministic_program(self, program: str, input_val: str) -> bool:
        """
        Validation déterministe selon l'équation (7)
        ∀p ∈ P_deterministic, ∀i ∈ I, lim_{j→∞} p(i)^{(1)} = p(i)^{(2)} = ... = p(i)^{(j)}
        """
        outputs = []
        
        for execution in range(self.j):  # j = 2 pour budget computationnel
            try:
                output = self.execute_program_safely(program, input_val)
                outputs.append(output)
            except Exception:
                return False  # Programme invalide
        
        # Vérifier que toutes les sorties sont identiques
        return all(output == outputs[0] for output in outputs)
    
    def verify_answer(self, task_type: str, task: Dict, solution: str) -> bool:
        """
        Vérification des réponses selon le type de tâche (Section 3.3.4)
        """
        if task_type == 'abd':  # Abduction: p(i_π) = p(i⋆)
            try:
                pred_output = self.execute_program_safely(task['program'], solution)
                true_output = self.execute_program_safely(task['program'], task['input'])
                return pred_output == true_output
            except:
                return False
        
        elif task_type == 'ded':  # Déduction: o_π = o⋆
            return solution == task['output']
        
        elif task_type == 'ind':  # Induction: all({p_π(i⋆_n) = o⋆_n}_N)
            try:
                for input_val, expected_output in zip(task['inputs'], task['outputs']):
                    actual_output = self.execute_program_safely(solution, input_val)
                    if actual_output != expected_output:
                        return False
                return True
            except:
                return False
        
        return False
    
    def trr_plus_plus_update(self, model: nn.Module, rewards: Dict) -> None:
        """
        Task-Relative REINFORCE++ (TRR++) - Section 3.3.5
        Calcul de baselines séparées pour chaque configuration task-role
        """
        # Mise à jour des statistiques pour chaque (task, role)
        for task_type in ['ded', 'abd', 'ind']:
            for role in ['propose', 'solve']:
                key = f"{task_type}_{role}"
                
                if key in rewards:
                    # Ajouter nouvelles récompenses
                    self.task_role_stats[key]['rewards'].extend(rewards[key])
                    
                    # Recalculer moyenne et écart-type
                    recent_rewards = self.task_role_stats[key]['rewards'][-100:]  # Fenêtre glissante
                    self.task_role_stats[key]['mean'] = np.mean(recent_rewards)
                    self.task_role_stats[key]['std'] = np.std(recent_rewards) + 1e-8
        
        # Calcul des avantages normalisés
        normalized_advantages = {}
        for task_type in ['ded', 'abd', 'ind']:
            for role in ['propose', 'solve']:
                key = f"{task_type}_{role}"
                if key in rewards:
                    stats = self.task_role_stats[key]
                    advantages = [(r - stats['mean']) / stats['std'] for r in rewards[key]]
                    normalized_advantages[key] = advantages
        
        # Mise à jour du modèle avec avantages normalisés
        self.update_model_parameters(model, normalized_advantages)
    
    def execute_program_safely(self, program: str, input_val: str) -> str:
        """
        Exécution sécurisée d'un programme Python
        """
        try:
            # Créer un fichier temporaire pour l'exécution
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(program)
                f.write(f"\nprint(f({repr(input_val)}))")
                temp_file = f.name
            
            # Exécuter le programme
            result = subprocess.run(['python', temp_file], 
                                  capture_output=True, text=True, timeout=5)
            
            # Nettoyer
            os.unlink(temp_file)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                raise RuntimeError(f"Execution error: {result.stderr}")
        
        except Exception as e:
            raise RuntimeError(f"Program execution failed: {e}")
    
    # Méthodes auxiliaires
    def sample_program_from_union(self) -> str:
        """Échantillonner programme de D_abd ∪ D_ded"""
        union_buffer = self.D_abd + self.D_ded
        return np.random.choice(union_buffer)['program']
    
    def sample_k_references(self, task_type: str) -> List[Dict]:
        """Échantillonner K références du buffer correspondant"""
        buffer = getattr(self, f'D_{task_type}')
        k = min(self.K, len(buffer))
        return np.random.choice(buffer, k, replace=False).tolist()
    
    def propose_task(self, model: nn.Module, task_type: str, k_refs: List) -> Tuple:
        """Proposer une nouvelle tâche"""
        # Implémentation simplifiée - à adapter selon le modèle
        return "def f(x): return x*2", "5", "10"
    
    def propose_induction_task(self, model: nn.Module, program: str) -> Tuple:
        """Proposer tâche d'induction"""
        # Implémentation simplifiée
        return ["1", "2", "3"], "Double the input"
    
    def solve_task(self, model: nn.Module, task_type: str, task: Dict) -> str:
        """Résoudre une tâche"""
        # Implémentation simplifiée - à adapter selon le modèle
        return "solution"
    
    def validate_task(self, program: str, input_val: str, output_val: str) -> bool:
        """Valider une tâche"""
        return self.validate_deterministic_program(program, input_val)
    
    def validate_induction_task(self, program: str, inputs: List, message: str) -> bool:
        """Valider une tâche d'induction"""
        return True  # Implémentation simplifiée
    
    def calculate_rewards(self, proposed_tasks: Dict, solved_tasks: Dict) -> Dict:
        """Calculer les récompenses"""
        return {}  # Implémentation simplifiée
    
    def update_model_parameters(self, model: nn.Module, advantages: Dict) -> None:
        """Mettre à jour les paramètres du modèle"""
        pass  # Implémentation simplifiée

# Exemple d'utilisation
azr_tech = AZRTechnicalImplementation(batch_size=64, k_references=5)
print("=== IMPLÉMENTATION TECHNIQUE AZR ===")
print("Algorithme complet avec gestion des buffers, validation déterministe et TRR++")
```

---

## ✅ POINTS CLÉS TECHNIQUES

### Innovations algorithmiques
1. **Buffers multiples** : Gestion séparée pour déduction, abduction, induction
2. **Validation déterministe** : j=2 exécutions pour vérifier la reproductibilité
3. **TRR++** : 6 baselines séparées pour réduction de variance optimale
4. **Bootstrap minimal** : Initialisation avec triplet identité simple

### Robustesse du système
- **Gestion des échecs** : Remplissage par échantillonnage des buffers
- **Diversité** : Promotion explicite de tâches différentes
- **Stabilité** : Validation rigoureuse à chaque étape
- **Scalabilité** : Architecture modulaire et extensible

---

*Documentation technique complète du fonctionnement interne d'AZR*
