#!/usr/bin/env python3
"""
Test simple des ajustements BCT
Validation rapide des nouvelles fonctionnalités
"""

import sys
sys.path.append('.')

from bct import AZRConfig, BaccaratGame, BaccaratCountingEngine

def test_nouvelles_methodes():
    """Test rapide des nouvelles méthodes"""
    print("🔬 TEST NOUVELLES MÉTHODES BCT")
    print("-" * 30)
    
    # Créer configuration et moteur
    config = AZRConfig()
    engine = BaccaratCountingEngine(config)
    
    # Créer jeu
    game = BaccaratGame(
        game_number=1,
        burn_cards_count=4,
        burn_parity='PAIR',
        initial_sync_state='SYNC',
        current_sync_state='SYNC'
    )
    
    # Ajouter quelques mains incluant TIE
    hands_data = [
        ('PLAYER', 4, 'pair_4'),
        ('TIE', 5, 'impair_5'),
        ('BANKER', 6, 'pair_6')
    ]
    
    print("Ajout des mains:")
    for result, cards, category in hands_data:
        hand = engine.process_hand(game, result, cards, category)
        print(f"  {result} {cards}c → {hand.combined_state}")
    
    print(f"\n📊 VALIDATION:")
    
    # Test méthodes nouvelles
    try:
        # get_full_influence_sequence
        influences = game.get_full_influence_sequence()
        print(f"✅ get_full_influence_sequence: {len(influences)} influences")
        
        # get_algorithmic_summary
        summary = game.get_algorithmic_summary()
        print(f"✅ get_algorithmic_summary: {len(summary)} clés")
        print(f"   - Total hands: {summary['total_hands']}")
        print(f"   - TIE hands: {summary['tie_hands']}")
        print(f"   - PB hands: {summary['pb_hands']}")
        
        # Vérifier TIE dans influences
        tie_count = sum(1 for inf in influences if inf['is_tie'])
        print(f"✅ TIE exploités: {tie_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    success = test_nouvelles_methodes()
    print(f"\n{'✅ SUCCÈS' if success else '❌ ÉCHEC'}")
    sys.exit(0 if success else 1)
