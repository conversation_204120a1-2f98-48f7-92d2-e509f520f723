{
  "model": "claude-4-sonnet",
  "temperature": 0.15,
  "top_p": 0.8,
  "max_tokens": 20480,
  "thinking_mode": "extended",
  "show_thinking": true,
  "system": "Tu es un architecte logiciel senior et expert Python avec 15+ ans d'expérience. Tu excelles dans l'analyse de projets complexes, l'adaptation de concepts mathématiques en code, et l'utilisation optimale d'outils de développement. Tu structures toujours tes réponses avec: 1) Vue d'ensemble, 2) Analyse technique, 3) Implémentation Python, 4) Validation mathématique, 5) Recommandations d'amélioration."
}

# Configuration Optimale - Expert Python & Analyse de Projets

## 🎯 Configuration Claude 4 Principale

### Paramètres API Optimaux
```json
{
  "model": "claude-4-sonnet",
  "temperature": 0.15,
  "top_p": 0.8,
  "max_tokens": 20480,
  "thinking_mode": "extended",
  "show_thinking": true,
  "system": "Tu es un architecte logiciel senior et expert Python avec 15+ ans d'expérience. Tu excelles dans l'analyse de projets complexes, l'adaptation de concepts mathématiques en code, et l'utilisation optimale d'outils de développement. Tu structures toujours tes réponses avec: 1) Vue d'ensemble, 2) Analyse technique, 3) Implémentation Python, 4) Validation mathématique, 5) Recommandations d'amélioration."
}
```

### Justification des Paramètres
- **Temperature 0.15** : Équilibre entre précision technique et créativité architecturale
- **Top_p 0.8** : Réponses cohérentes avec variabilité contrôlée
- **Max_tokens 20480** : Analyses approfondies et code complet
- **Extended thinking** : Raisonnement visible pour projets complexes

---

## 🛠️ Stratégie d'Utilisation des Outils

### 1. Vue d'Ensemble de Projet (Workflow Optimisé)

#### Phase 1 - Reconnaissance Initiale
```
1. codebase-retrieval: "Analyse l'architecture globale du projet, les modules principaux, les dépendances, les patterns utilisés, et la structure des données"

2. view: Examiner les fichiers clés (README, requirements.txt, main.py, config)

3. diagnostics: Identifier les erreurs et warnings existants
```

#### Phase 2 - Analyse Approfondie
```
4. codebase-retrieval: "Détaille les classes principales, leurs méthodes, les algorithmes utilisés, et les points d'intégration entre modules"

5. view avec regex: Rechercher patterns spécifiques
   - "class.*:" pour les classes
   - "def.*:" pour les fonctions
   - "import.*" pour les dépendances

6. render-mermaid: Créer diagrammes d'architecture
```

### 2. Expertise Python (Configuration Spécialisée)

#### System Prompt Python Expert
```
"Tu es un Python expert niveau senior (15+ ans). Tu maîtrises:
- Architecture: Design patterns, SOLID, Clean Architecture
- Performance: Profiling, optimisation algorithmique, async/await
- Écosystème: FastAPI, Django, SQLAlchemy, Pandas, NumPy, SciPy
- Qualité: Type hints, docstrings, tests (pytest), linting (black, mypy)
- Mathématiques: NumPy, SciPy, SymPy pour implémentations scientifiques
- Bonnes pratiques: PEP 8, code review, documentation

Structure tes réponses:
1. Analyse du problème
2. Solution Python optimale
3. Code avec type hints et docstrings
4. Tests unitaires
5. Optimisations possibles"
```

### 3. Adaptation Mathématiques → Code

#### Configuration Spécialisée Math-Code
```json
{
  "temperature": 0.1,
  "top_p": 0.75,
  "max_tokens": 16384,
  "system": "Tu es un expert en mathématiques computationnelles. Tu traduis concepts mathématiques en code Python efficace. Utilise NumPy/SciPy pour calculs numériques, SymPy pour symbolique. Toujours: 1) Explique la théorie, 2) Montre l'équation, 3) Implémente en Python, 4) Valide avec tests, 5) Optimise les performances."
}
```

### 4. Utilisation Puissante des Outils

#### Workflow de Développement Optimal
```
Étape 1 - Compréhension:
- codebase-retrieval: Architecture et contexte
- view: Code existant avec patterns regex
- remember: Points clés du projet

Étape 2 - Recherche:
- web-search: Solutions et best practices
- web-fetch: Documentation technique détaillée

Étape 3 - Développement:
- str-replace-editor: Modifications précises
- save-file: Nouveaux modules/tests
- render-mermaid: Documentation visuelle

Étape 4 - Validation:
- launch-process: Tests et linting
- diagnostics: Vérification erreurs
- read-terminal: Monitoring continu
```

### 5. Relecture de Conversation

#### Stratégie de Mémorisation
```
1. remember: Stocker les décisions architecturales importantes
2. save-file: Créer des notes de session pour contexte long
3. view: Relire les fichiers de notes créés
4. codebase-retrieval: Retrouver le contexte technique précédent
```

#### Template de Notes de Session
```markdown
# Session Notes - [Date]

## Objectifs du Projet
- [Objectif principal]
- [Contraintes techniques]
- [Exigences performance]

## Décisions Architecturales
- [Pattern choisi et justification]
- [Technologies sélectionnées]
- [Structure de données]

## Équations/Algorithmes Implémentés
- [Formule mathématique]
- [Implémentation Python]
- [Tests de validation]

## Actions Suivantes
- [Tâches prioritaires]
- [Points à approfondir]
```

### 6. Recherches Internet Efficaces

#### Stratégie de Recherche Multi-Phase
```
Phase 1 - Recherche Générale:
web-search: "[concept] Python implementation best practices" (5 résultats)

Phase 2 - Recherche Technique:
web-search: "[specific algorithm] NumPy SciPy optimization" (10 résultats)

Phase 3 - Recherche Comparative:
web-search: "[solution A] vs [solution B] performance benchmark" (10 résultats)

Phase 4 - Recherche Documentation:
web-fetch: Documentation officielle des bibliothèques
```

#### Mots-clés Optimaux par Domaine
```
Architecture Python:
- "Python design patterns implementation"
- "Clean architecture Python example"
- "SOLID principles Python code"

Performance:
- "Python optimization profiling"
- "NumPy vectorization performance"
- "async await Python best practices"

Mathématiques:
- "mathematical algorithm Python implementation"
- "NumPy SciPy scientific computing"
- "symbolic math SymPy Python"

Testing:
- "pytest advanced testing patterns"
- "Python test driven development"
- "property based testing hypothesis"
```

---

## 🚀 Workflow Complet d'Analyse de Projet

### Étape 1: Reconnaissance (5-10 minutes)
```bash
1. codebase-retrieval: "Vue d'ensemble complète du projet"
2. view: README.md, requirements.txt, setup.py
3. diagnostics: ["./"]
4. remember: "Projet [nom] - [description courte] - [tech stack]"
```

### Étape 2: Architecture (10-15 minutes)
```bash
5. codebase-retrieval: "Architecture détaillée, modules, classes principales"
6. view avec regex: "class.*:|def.*:|import.*"
7. render-mermaid: Diagramme d'architecture
8. save-file: "architecture_analysis.md"
```

### Étape 3: Code Analysis (15-20 minutes)
```bash
9. codebase-retrieval: "Algorithmes, patterns, points d'optimisation"
10. view: Fichiers core avec analyse approfondie
11. web-search: Best practices pour les patterns identifiés
12. save-file: "code_analysis_recommendations.md"
```

### Étape 4: Implémentation (Variable)
```bash
13. str-replace-editor: Modifications/améliorations
14. launch-process: Tests et validation
15. diagnostics: Vérification post-modification
16. save-file: Documentation des changements
```

---

## 🎯 Prompts Spécialisés par Tâche

### Analyse de Projet
```
"Analyse ce projet Python en tant qu'architecte senior. Identifie:
1. Architecture globale et patterns utilisés
2. Points forts et faiblesses du design
3. Opportunités d'optimisation
4. Conformité aux best practices Python
5. Recommandations d'amélioration prioritaires"
```

### Implémentation Mathématique
```
"Implémente cette équation/algorithme mathématique en Python:
1. Explique la théorie mathématique
2. Choisis les bibliothèques optimales (NumPy/SciPy/SymPy)
3. Code avec type hints et docstrings complètes
4. Inclus tests de validation numérique
5. Optimise pour performance et précision"
```

### Code Review
```
"Review ce code Python en tant qu'expert senior:
1. Conformité PEP 8 et best practices
2. Performance et optimisations possibles
3. Sécurité et robustesse
4. Maintenabilité et lisibilité
5. Tests et documentation manquants"
```

---

## 📊 Métriques de Qualité

### KPIs de Performance
- **Temps d'analyse** : <30 minutes pour projet moyen
- **Précision technique** : >95% des recommandations valides
- **Couverture** : 100% des modules principaux analysés
- **Actionabilité** : Recommandations concrètes et implémentables

### Validation Continue
- Tests automatisés après chaque modification
- Profiling de performance sur code critique
- Review de conformité aux standards Python
- Documentation à jour avec changements

---

## 🔧 Templates Prêts à l'Emploi

### Template d'Analyse Complète de Projet
```python
"""
Template d'analyse de projet Python
Utilise ce template pour chaque nouveau projet
"""

# 1. Reconnaissance initiale
codebase_query_1 = """
Analyse l'architecture globale de ce projet Python:
- Structure des dossiers et modules
- Technologies et frameworks utilisés
- Patterns architecturaux (MVC, Clean Architecture, etc.)
- Points d'entrée principaux
- Configuration et dépendances
- Tests existants et couverture
"""

# 2. Analyse technique approfondie
codebase_query_2 = """
Détaille l'implémentation technique:
- Classes principales et leurs responsabilités
- Algorithmes et structures de données utilisés
- Gestion des erreurs et logging
- Performance et optimisations
- Sécurité et validation des données
- Intégrations externes (APIs, bases de données)
"""

# 3. Analyse mathématique/scientifique (si applicable)
codebase_query_3 = """
Identifie les aspects mathématiques/scientifiques:
- Équations et formules implémentées
- Bibliothèques scientifiques utilisées (NumPy, SciPy, etc.)
- Algorithmes de calcul et optimisation
- Précision numérique et gestion des erreurs
- Visualisations et analyses de données
"""
```

### Template de Recherche Internet Optimisée
```python
"""
Stratégie de recherche pour problèmes Python complexes
"""

def recherche_optimisee(probleme, contexte):
    """
    Étapes de recherche pour résoudre un problème Python
    """

    # Étape 1: Recherche générale
    query_1 = f"{probleme} Python best practices implementation"

    # Étape 2: Recherche technique spécialisée
    query_2 = f"{probleme} {contexte} optimization performance"

    # Étape 3: Recherche comparative
    query_3 = f"{probleme} alternatives comparison benchmark"

    # Étape 4: Documentation officielle
    query_4 = f"{probleme} official documentation tutorial"

    return [query_1, query_2, query_3, query_4]

# Exemples d'usage:
# recherche_optimisee("async programming", "web scraping")
# recherche_optimisee("machine learning", "time series forecasting")
# recherche_optimisee("database optimization", "SQLAlchemy PostgreSQL")
```

### Template d'Implémentation Mathématique
```python
"""
Template pour implémenter des concepts mathématiques en Python
"""

import numpy as np
import scipy as sp
from typing import Union, Tuple, Optional
import pytest

def implement_mathematical_concept(
    equation: str,
    parameters: dict,
    validation_data: Optional[dict] = None
) -> Tuple[callable, list]:
    """
    Template d'implémentation mathématique

    Args:
        equation: Description de l'équation/algorithme
        parameters: Paramètres et leurs types
        validation_data: Données pour tests de validation

    Returns:
        Tuple[fonction_implémentée, tests_unitaires]
    """

    # 1. Analyse théorique
    theory_analysis = f"""
    Équation: {equation}
    Paramètres: {parameters}
    Domaine de validité: [à définir]
    Complexité algorithmique: [à calculer]
    """

    # 2. Implémentation NumPy/SciPy
    def mathematical_function(x: np.ndarray, **kwargs) -> np.ndarray:
        """
        Implémentation optimisée de l'équation

        Args:
            x: Données d'entrée
            **kwargs: Paramètres de l'équation

        Returns:
            Résultat calculé

        Raises:
            ValueError: Si les paramètres sont invalides
        """
        # Validation des entrées
        if not isinstance(x, np.ndarray):
            x = np.asarray(x)

        # Implémentation de l'équation
        # [Code spécifique à implémenter]

        return result

    # 3. Tests de validation
    def test_mathematical_function():
        """Tests unitaires pour validation"""
        # Test avec données connues
        # Test des cas limites
        # Test de performance
        pass

    return mathematical_function, [test_mathematical_function]
```

---

## 🎯 Exemples Concrets d'Utilisation

### Exemple 1: Analyse d'un Projet de Machine Learning
```bash
# Étape 1: Vue d'ensemble
codebase-retrieval: "Analyse ce projet ML: architecture, datasets, modèles, pipeline d'entraînement, métriques d'évaluation"

# Étape 2: Analyse des algorithmes
view: "models/" avec regex "class.*Model|def train|def predict"

# Étape 3: Recherche d'optimisations
web-search: "machine learning pipeline optimization Python scikit-learn"

# Étape 4: Validation mathématique
codebase-retrieval: "Équations et formules utilisées dans les modèles, fonctions de coût, optimiseurs"
```

### Exemple 2: Optimisation d'un Algorithme Numérique
```bash
# Étape 1: Identification du goulot d'étranglement
diagnostics: ["./"] + launch-process: "python -m cProfile main.py"

# Étape 2: Analyse de l'algorithme
codebase-retrieval: "Algorithmes de calcul intensif, boucles, opérations matricielles"

# Étape 3: Recherche de solutions
web-search: "NumPy vectorization optimization performance Python"

# Étape 4: Implémentation optimisée
str-replace-editor: Remplacer boucles par opérations vectorisées
```

### Exemple 3: Intégration d'une Nouvelle Bibliothèque Mathématique
```bash
# Étape 1: Recherche de compatibilité
web-search: "[nouvelle_lib] integration existing Python project"

# Étape 2: Analyse d'impact
codebase-retrieval: "Points d'intégration possibles, dépendances existantes, conflits potentiels"

# Étape 3: Plan d'implémentation
render-mermaid: Diagramme d'intégration

# Étape 4: Tests de validation
save-file: "integration_tests.py" avec tests complets
```

---

## 🚀 Workflow d'Expertise Python Avancée

### Phase 1: Diagnostic Rapide (2-3 minutes)
```python
# Commandes rapides pour évaluation initiale
commands = [
    "view: requirements.txt",
    "view: setup.py ou pyproject.toml",
    "diagnostics: ['./']",
    "codebase-retrieval: 'Technologies utilisées, structure générale'"
]
```

### Phase 2: Analyse Architecturale (5-10 minutes)
```python
# Analyse approfondie de l'architecture
analysis_commands = [
    "codebase-retrieval: 'Patterns architecturaux, séparation des responsabilités'",
    "view avec regex: 'class.*:|def.*main|if __name__'",
    "render-mermaid: Diagramme de classes principales",
    "web-search: 'Python architecture patterns best practices'"
]
```

### Phase 3: Optimisation et Amélioration (10-20 minutes)
```python
# Identification et implémentation d'améliorations
optimization_commands = [
    "codebase-retrieval: 'Goulots d'étranglement, code dupliqué, optimisations possibles'",
    "launch-process: 'python -m pytest --cov=. --cov-report=html'",
    "web-search: 'Python performance optimization profiling'",
    "str-replace-editor: Implémentation des améliorations"
]
```

---

## 📋 Checklist de Qualité Python

### ✅ Architecture et Design
- [ ] Respect des principes SOLID
- [ ] Séparation claire des responsabilités
- [ ] Patterns appropriés au contexte
- [ ] Configuration externalisée
- [ ] Gestion d'erreurs robuste

### ✅ Code Quality
- [ ] Conformité PEP 8
- [ ] Type hints complets
- [ ] Docstrings détaillées
- [ ] Noms explicites et cohérents
- [ ] Fonctions pures quand possible

### ✅ Performance
- [ ] Profiling des sections critiques
- [ ] Utilisation optimale de NumPy/SciPy
- [ ] Éviter les boucles Python inutiles
- [ ] Gestion mémoire efficace
- [ ] Async/await pour I/O

### ✅ Tests et Validation
- [ ] Couverture de tests >90%
- [ ] Tests unitaires, intégration, e2e
- [ ] Property-based testing si applicable
- [ ] Validation des données d'entrée
- [ ] Tests de performance

### ✅ Documentation
- [ ] README complet avec exemples
- [ ] Documentation API générée
- [ ] Diagrammes d'architecture
- [ ] Guide de contribution
- [ ] Changelog maintenu

---

*Configuration optimisée pour expertise Python et analyse de projets*
*Version 2.0 - Avec templates pratiques et workflows détaillés*
*Basée sur recherches approfondies Claude 4 et configurations optimales*
